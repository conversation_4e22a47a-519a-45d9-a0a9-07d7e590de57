import React from 'react'

import dynamic from 'next/dynamic'

const MediaHero = dynamic(() => import('ui/data-display/MediaHero'))
const Carousel = dynamic(() => import('ui/data-display/Carousel'))

export default function HeroCarousel({
  items,
  duration,
  gradientBaseColor,
  titleTextSize,
  descriptionTextSize,
  showDescriptionMobile,
  ctaVariant,
}) {
  const Hero = ({ item }) => (
    <MediaHero
      title={item?.title}
      titleTextSize={titleTextSize}
      description={item?.description}
      descriptionTextSize={descriptionTextSize}
      showDescriptionMobile={showDescriptionMobile}
      image={item?.image}
      mobileImage={item?.mobileImage}
      logo={item?.logo}
      ctaLabel={item?.callToAction}
      ctaUrl={item?.url}
      ctaVariant={ctaVariant}
      gradientBaseColor={gradientBaseColor}
    />
  )

  return (
    <>
      {items.length === 1 && <Hero item={items[0]} />}
      {items.length > 1 && (
        <Carousel duration={duration}>
          {items.map((item, i) => (
            <Hero key={`heroslide-${i}`} item={item} />
          ))}
        </Carousel>
      )}
    </>
  )
}
