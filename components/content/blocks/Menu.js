import PropTypes from 'prop-types'
import { useMemo } from 'react'

import clsx from 'clsx'
import dynamic from 'next/dynamic'
import { getBackgroundColor, getTextColor } from 'ui/helpers/getColor'
import useBorder from 'ui/helpers/useBorder'
import useBorderRadius from 'ui/helpers/useBorderRadius'
import useDivider from 'ui/helpers/useDivider'
import useFlex from 'ui/helpers/useFlex'
import useTextSize from 'ui/helpers/useTextSize'
import usePadding from 'ui/helpers/usePadding'
import useSpacing from 'ui/helpers/useSpacing'
import useTextAlign from 'ui/helpers/useTextAlign'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'
import useWidth from 'ui/helpers/useWidth'
import { useFontFamily } from 'ui/helpers/useFontFamily'
import useFontWeight from 'ui/helpers/useFontWeight'

const DropdownMenu = dynamic(() => import('ui/navigation/DropdownMenu'))
const UIMenu = dynamic(() => import('ui/navigation/Menu'))

export default function Menu({
  align,
  border,
  borderRadius,
  className,
  direction,
  dropdownAlignment,
  fontFamily,
  fontWeight,
  icon = 'bars',
  iconColor,
  iconSize = 'md',
  id,
  itemActiveBgColor,
  itemActiveColor,
  itemBgColor,
  itemColor,
  itemDivider,
  itemPadding,
  itemTextAlign,
  justify,
  menu,
  menuBgColor,
  offset,
  openedIcon = 'xmark',
  openedIconColor,
  padding,
  presentation,
  spacing,
  textCase,
  textSize,
  width,
  pageData,
}) {
  const { site } = pageData || {}
  const bgColor = useValueAtBreakpoint(menuBgColor)
  const iconColorValue = useValueAtBreakpoint(iconColor)
  const openedIconColorValue = useValueAtBreakpoint(openedIconColor)
  const itemActiveBgColorValue = useValueAtBreakpoint(itemActiveBgColor)
  const itemActiveColorValue = useValueAtBreakpoint(itemActiveColor)
  const itemBgColorValue = useValueAtBreakpoint(itemBgColor)
  const itemColorValue = useValueAtBreakpoint(itemColor)
  const presentationValue = useValueAtBreakpoint(presentation)
  const offsetValue = useValueAtBreakpoint(offset)
  const directionValue = useValueAtBreakpoint(direction)

  const borderClasses = useBorder(border)
  const borderRadiusClasses = useBorderRadius(borderRadius)
  const dividerClasses = useDivider(itemDivider, directionValue)
  const flexClasses = useFlex(direction, align, justify)
  const spacingClasses = useSpacing(spacing)
  const paddingClasses = usePadding(padding)
  const bgColorClass = getBackgroundColor(bgColor)
  const iconColorClass =
    getTextColor(iconColorValue) ?? getTextColor(itemColorValue)
  const openedIconColorClass =
    getTextColor(openedIconColorValue) ??
    iconColorClass ??
    getTextColor(itemActiveColorValue) ??
    getTextColor(itemColorValue)
  const itemPaddingClass = usePadding(itemPadding)
  const textSizeClass = useTextSize(textSize)
  const textCaseClass = useValueAtBreakpoint(textCase)
  const alignClass = useTextAlign(itemTextAlign)
  const widthClass = useWidth(width)
  const fontFamilyClass = useFontFamily(fontFamily)
  const fontWeightClass = useFontWeight(fontWeight)

  const classNames = useMemo(
    () =>
      clsx(
        {
          [bgColorClass]: presentationValue !== 'dropdown',
          [borderClasses]: borderClasses && presentationValue !== 'dropdown',
          [borderRadiusClasses]:
            borderRadiusClasses && presentationValue !== 'dropdown',
          [dividerClasses]: dividerClasses && presentationValue !== 'dropdown',
        },
        className,
        flexClasses,
        paddingClasses,
        spacingClasses,
        widthClass
      ),
    [
      bgColorClass,
      borderClasses,
      borderRadiusClasses,
      className,
      dividerClasses,
      flexClasses,
      paddingClasses,
      presentationValue,
      spacingClasses,
      widthClass,
    ]
  )

  const itemClassName = useMemo(
    () =>
      clsx(
        {
          [getBackgroundColor(itemBgColorValue)]: itemBgColorValue,
          [`hover:${getBackgroundColor(itemBgColorValue)}`]:
            itemActiveBgColorValue,
          [`hover:${getTextColor(itemActiveColorValue)}`]: itemActiveColorValue,
        },
        getTextColor(itemColorValue),
        itemPaddingClass,
        alignClass,
        textSizeClass,
        textCaseClass,
        fontFamilyClass,
        fontWeightClass
      ),
    [
      itemActiveBgColorValue,
      itemActiveColorValue,
      itemBgColorValue,
      itemColorValue,
      itemPaddingClass,
      alignClass,
      textSizeClass,
      textCaseClass,
      fontFamilyClass,
      fontWeightClass,
    ]
  )

  const activeItemClassName = useMemo(
    () =>
      clsx(
        getBackgroundColor(itemActiveBgColorValue),
        getTextColor(itemActiveColorValue),
        itemPaddingClass,
        alignClass,
        textSizeClass,
        textCaseClass,
        fontFamilyClass,
        fontWeightClass
      ),
    [
      itemActiveBgColorValue,
      itemActiveColorValue,
      itemPaddingClass,
      alignClass,
      textSizeClass,
      textCaseClass,
      fontFamilyClass,
      fontWeightClass,
    ]
  )

  const commonProps = {
    activeItemClassName,
    align,
    bgColor,
    border,
    className: classNames,
    itemColor: itemColorValue,
    direction,
    fontFamily,
    id,
    itemClassName,
    items: menu,
    justify,
    padding,
    spacing,
    site,
  }

  if (!menu) return null

  if (presentationValue === 'dropdown') {
    return (
      <DropdownMenu
        {...commonProps}
        alignment={dropdownAlignment}
        bgColor={bgColor}
        borderRadius={borderRadius}
        direction={directionValue}
        divider={itemDivider}
        icon={icon}
        iconClassName={iconColorClass}
        iconSize={iconSize}
        offset={offsetValue}
        openedIcon={openedIcon}
        openedIconClassName={openedIconColorClass}
        panelClass={widthClass}
      />
    )
  }

  return <UIMenu {...commonProps} />
}
Menu.propTypes = {
  align: PropTypes.object,
  border: PropTypes.object,
  borderRadius: PropTypes.object,
  className: PropTypes.string,
  direction: PropTypes.object,
  dropdownAlignment: PropTypes.object,
  fontFamily: PropTypes.string,
  fontWeight: PropTypes.string,
  icon: PropTypes.string,
  iconColor: PropTypes.object,
  iconSize: PropTypes.object,
  id: PropTypes.string,
  itemActiveBgColor: PropTypes.object,
  itemActiveColor: PropTypes.object,
  itemBgColor: PropTypes.object,
  itemColor: PropTypes.object,
  itemDivider: PropTypes.object,
  itemPadding: PropTypes.object,
  itemTextAlign: PropTypes.object,
  justify: PropTypes.object,
  menu: PropTypes.array,
  menuBgColor: PropTypes.object,
  offset: PropTypes.object,
  openedIcon: PropTypes.string,
  openedIconColor: PropTypes.object,
  padding: PropTypes.object,
  presentation: PropTypes.object,
  spacing: PropTypes.object,
  textCase: PropTypes.object,
  width: PropTypes.object,
}
