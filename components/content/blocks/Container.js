import clsx from 'clsx'
import { useMemo } from 'react'

import { getBackgroundGradientStyle } from 'ui/helpers/getBackgroundGradientStyle'
import { getBackgroundColor } from 'ui/helpers/getColor'
import useBackgroundImage from 'ui/helpers/useBackroundImage'
import useFlex from 'ui/helpers/useFlex'
import useMaxWidth from 'ui/helpers/useMaxWidth'
import usePadding from 'ui/helpers/usePadding'
import usePosition from 'ui/helpers/usePosition'
import { usePositionOffset } from 'ui/helpers/usePositionOffset'
import useSpacing from 'ui/helpers/useSpacing'
import { useTransformTranslate } from 'ui/helpers/useTransformTranslate'
import { useValueAtBreakpoint } from 'ui/helpers/useValueAtBreakpoint'

/**
 * @typedef {import('ui/helpers/getBackgroundGradientStyle').BgGradient} BgGradient The background gradient of the container.
 * @typedef {import('ui/helpers/usePositionOffset').PositionOffset} PositionOffset The position offset of the container.
 * @typedef {import('ui/helpers/useTransformTranslate').TransformTranslate} TransformTranslate The translation of the container.
 */

/**
 * A Container component that can be used to 'contain' other Block in a specific width and apply styles it (bgColor, bgGradient, bgImage, etc).
 * @param {Object} props
 * @param {Object} props.align The alignment of the children inside the container.
 * @param {string} props.bgColor The background color of the container.
 * @param {BgGradient} props.bgGradient The background gradient of the container.
 * @param {Object} props.bgImage The background image of the container.
 * @param {string} props.bgImageAlign The alignment of the background image.
 * @param {'solid'|'gradient'} props.bgType The type of the background.
 * @param {React.ReactNode} props.children The children of the container.
 * @param {string} props.className The class name of the container.
 * @param {Object} props.direction The direction of the container.
 * @param {string} props.id The id of the container.
 * @param {Object} props.justify The justification of the children inside the container.
 * @param {Object} props.maxWidth The maximum width of the container.
 * @param {Object} props.padding The padding of the container.
 * @param {Object} props.position The position of the container.
 * @param {PositionOffset} props.positionOffset The position offset of the container.
 * @param {Object} props.spacing The spacing of the container.
 * @param {TransformTranslate} props.transformTranslate The translation of the container.
 * @param {Object} props.visibility The visibility of the container.
 * @returns {React.ReactElement} The Container component.
 */
export default function Container({
  align,
  bgColor,
  bgGradient,
  bgImage,
  bgImageAlign,
  bgType,
  children,
  className = '',
  direction,
  id,
  justify,
  maxWidth,
  padding,
  position,
  positionOffset,
  spacing,
  transformTranslate,
  visibility,
}) {
  const bgColorClass = getBackgroundColor(bgColor)
  const positionClass = usePosition(position)
  const paddingClass = usePadding(padding)
  const flexClasses = useFlex(direction ?? { xs: 'y' }, align, justify)
  const spacingClass = useSpacing(spacing, 'lg')
  const maxWidthClass = useMaxWidth(maxWidth)

  const positionOffsetStyles = usePositionOffset(position, positionOffset)
  const transformStyles = useTransformTranslate(transformTranslate)

  const bgImageStyle = useBackgroundImage(bgImage, bgImageAlign, 1920)

  const bgGradientStyle = useMemo(() => {
    if (!bgGradient || bgType === 'solid') return null

    return getBackgroundGradientStyle(bgGradient)
  }, [bgGradient, bgType])

  const containerStyle = useMemo(() => {
    return {
      ...(position === 'static' && bgType === 'solid'
        ? {}
        : positionOffsetStyles || {}),
      ...(transformStyles || {}),
      ...((!bgImage && bgGradientStyle) || {}),
      ...(bgImageStyle || {}),
    }
  }, [
    position,
    bgType,
    positionOffsetStyles,
    transformStyles,
    bgImage,
    bgGradientStyle,
    bgImageStyle,
  ])

  const visibilityValue = useValueAtBreakpoint(visibility, 'visible')

  if (visibilityValue === 'hidden') {
    return null
  }

  return (
    <div
      data-type="Container"
      className={clsx(
        '!mx-auto',
        {
          'bg-cover bg-no-repeat': bgImage,
          'max-w-screen-xl': !maxWidthClass,
          'relative': positionClass === 'static',
          [positionClass]: positionClass !== 'static',
          'xl:px-12 2xl:px-0': !paddingClass,
        },
        spacingClass,
        flexClasses,
        paddingClass,
        bgColorClass,
        maxWidthClass,
        className
      )}
      id={id}
      style={containerStyle}
    >
      {children}
    </div>
  )
}
