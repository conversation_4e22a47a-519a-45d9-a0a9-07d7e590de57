import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

const Link = dynamic(() => import('ui/navigation/Link'))
const Image = dynamic(() => import('ui/data-display/Image'))

export default function PublicationPoster({
  publication,
  showTitle = true,
  showHover = true,
  className = '',
  imageClass = '',
}) {
  return (
    <Link to={publication.url}>
      <div
        className={`flex flex-col gap-4 rounded-xl border border-transparent ${
          showHover ? '-m-4 p-4 hover:border-gray-200 hover:bg-gray-100' : ''
        } ${className}`}
      >
        <Image
          className={`rounded-xl shadow-md ${imageClass}`}
          aspectRatio="2/3"
          file={publication.cover?.file}
          sizes="253px"
        />
        {showTitle && (
          <h3 className="text-lg text-gray-700">{publication.title}</h3>
        )}
      </div>
    </Link>
  )
}
PublicationPoster.propTypes = {
  publication: PropTypes.object.isRequired,
  showDescription: PropTypes.bool,
  showHover: PropTypes.bool,
}
