import React, { useCallback, useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { CookieConsentContext } from './hooks'

const cookiesConsentKey = 'CookiesConsent'

export default function CookieConsentProvider({ children, settings = {} }) {
  const [userConsent, setUserConsent] = useState(null)
  const [showCookieConsent, setShowCookieConsent] = useState(false)

  // If exsists, load previously saved user consent from local storage
  useEffect(() => {
    async function getCookieConsent() {
      const storedConsent = await JSON.parse(
        localStorage.getItem(cookiesConsentKey)
      )
      setUserConsent(storedConsent || {})

      if (!storedConsent) {
        setShowCookieConsent(true)
      }
    }

    getCookieConsent()
  }, [])

  // Updates user consent
  const updateUserConsent = useCallback(value => {
    setUserConsent(value)
    setShowCookieConsent(false)
    localStorage.setItem(cookiesConsentKey, JSON.stringify(value))
  }, [])

  return (
    <CookieConsentContext.Provider
      value={{
        disabled: settings?.disabled,
        showCookieConsent,
        setShowCookieConsent,
        userConsent,
        updateUserConsent,
      }}
    >
      {children}
    </CookieConsentContext.Provider>
  )
}
CookieConsentProvider.propTypes = {
  children: PropTypes.node,
  settings: PropTypes.object,
}
