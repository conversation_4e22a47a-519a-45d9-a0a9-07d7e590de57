import { defaultCache } from '@serwist/next/worker'
import { Serwist, NetworkOnly, NetworkFirst } from 'serwist'

const OFFLINE_FALLBACK_PAGE = '/offline.html'

const serwist = new Serwist({
  precacheEntries: self.__SW_MANIFEST || [],
  precacheOptions: {
    cleanupOutdatedCaches: true,
    concurrency: 10,
    fallbackToNetwork: true,
    ignoreURLParametersMatching: [],
  },
  skipWaiting: true,
  clientsClaim: true,
  navigationPreload: true,
  fallbacks: {
    // Make sure the offline page is precached
    document: OFFLINE_FALLBACK_PAGE,
  },
  runtimeCaching: [
    // Add a specific rule for navigation requests
    {
      matcher: ({ request }) => request.mode === 'navigate',
      handler: new NetworkFirst({
        networkTimeoutSeconds: 3,
        cacheName: 'pages-cache',
        plugins: [
          {
            handlerDidError: async () => {
              // Try to return the offline page from the cache
              const cachedResponse = await caches.match(OFFLINE_FALLBACK_PAGE)
              if (cachedResponse) return cachedResponse

              // If offline page is not in cache, try to fetch it
              return fetch(OFFLINE_FALLBACK_PAGE).catch(() => {
                // If all fails, return a simple offline message
                return new Response('You are offline', {
                  status: 503,
                  headers: { 'Content-Type': 'text/html' },
                })
              })
            },
          },
        ],
      }),
    },
    ...defaultCache,
    {
      matcher: ({ url }) => url.pathname === '/site.webmanifest',
      handler: new NetworkOnly(),
    },
  ],
})

serwist.addEventListeners()
