import { useState, useCallback } from 'react'
import { downloadVideoBlob, storeVideo, isValidVideoUrl } from '../utils/videoStorage'

/**
 * Custom hook for video download functionality
 */
export default function useVideoDownload() {
  const [isDownloading, setIsDownloading] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [downloadError, setDownloadError] = useState(null)

  const downloadVideo = useCallback(async (url) => {
    if (!url || !url.trim()) {
      throw new Error('Please provide a valid video URL')
    }

    const trimmedUrl = url.trim()

    // Basic URL validation
    if (!isValidVideoUrl(trimmedUrl)) {
      throw new Error('Please provide a valid video URL (must be a direct video file or from a supported platform)')
    }

    setIsDownloading(true)
    setDownloadProgress(0)
    setDownloadError(null)

    try {
      // Download the video blob with progress tracking
      const blob = await downloadVideoBlob(trimmedUrl, (progress) => {
        setDownloadProgress(progress)
      })

      // Validate that we got a video blob
      if (!blob || blob.size === 0) {
        throw new Error('Failed to download video: empty file received')
      }

      // Check if it's actually a video file
      if (!blob.type.startsWith('video/') && !blob.type.startsWith('application/')) {
        // Try to determine if it's a video based on content
        const arrayBuffer = await blob.slice(0, 12).arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)
        
        // Check for common video file signatures
        const isVideo = checkVideoSignature(uint8Array)
        
        if (!isVideo) {
          throw new Error('The downloaded file does not appear to be a video')
        }
      }

      // Store the video in IndexedDB
      const videoData = {
        originalUrl: trimmedUrl,
        blob: blob,
        title: extractTitleFromUrl(trimmedUrl)
      }

      await storeVideo(videoData)
      
      setDownloadProgress(100)
      
      // Reset state after a short delay
      setTimeout(() => {
        setIsDownloading(false)
        setDownloadProgress(0)
      }, 1000)

    } catch (error) {
      setIsDownloading(false)
      setDownloadProgress(0)
      setDownloadError(error.message)
      throw error
    }
  }, [])

  const clearError = useCallback(() => {
    setDownloadError(null)
  }, [])

  return {
    downloadVideo,
    isDownloading,
    downloadProgress,
    downloadError,
    clearError
  }
}

/**
 * Check if the file has a video signature
 */
function checkVideoSignature(uint8Array) {
  // MP4 signature
  if (uint8Array.length >= 8) {
    const mp4Signature = uint8Array.slice(4, 8)
    const mp4Types = [
      [0x66, 0x74, 0x79, 0x70], // 'ftyp'
    ]
    
    for (const signature of mp4Types) {
      if (signature.every((byte, index) => byte === mp4Signature[index])) {
        return true
      }
    }
  }

  // WebM signature
  if (uint8Array.length >= 4) {
    const webmSignature = [0x1A, 0x45, 0xDF, 0xA3]
    if (webmSignature.every((byte, index) => byte === uint8Array[index])) {
      return true
    }
  }

  // AVI signature
  if (uint8Array.length >= 12) {
    const aviSignature1 = [0x52, 0x49, 0x46, 0x46] // 'RIFF'
    const aviSignature2 = [0x41, 0x56, 0x49, 0x20] // 'AVI '
    
    if (aviSignature1.every((byte, index) => byte === uint8Array[index]) &&
        aviSignature2.every((byte, index) => byte === uint8Array[index + 8])) {
      return true
    }
  }

  // MOV/QuickTime signature
  if (uint8Array.length >= 8) {
    const movSignature = uint8Array.slice(4, 8)
    const movTypes = [
      [0x71, 0x74, 0x20, 0x20], // 'qt  '
      [0x6D, 0x6F, 0x6F, 0x76], // 'moov'
    ]
    
    for (const signature of movTypes) {
      if (signature.every((byte, index) => byte === movSignature[index])) {
        return true
      }
    }
  }

  return false
}

/**
 * Extract title from URL
 */
function extractTitleFromUrl(url) {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const filename = pathname.split('/').pop()
    
    if (filename && filename.includes('.')) {
      return filename.split('.').slice(0, -1).join('.')
    }
    
    return filename || `Video from ${urlObj.hostname}`
  } catch {
    return 'Downloaded Video'
  }
}
