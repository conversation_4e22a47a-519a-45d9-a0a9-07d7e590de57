import { useState, useEffect, useCallback } from 'react'
import {
  getAllVideos,
  deleteVideo as deleteVideoFromDB,
  getStorageUsage as getStorageUsageFromDB,
  clearAllVideos,
} from '../utils/videoStorage'

/**
 * Custom hook for managing video storage
 */
export default function useVideoStorage() {
  const [videos, setVideos] = useState([])
  const [storageUsage, setStorageUsage] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load videos from IndexedDB
  const loadVideos = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const storedVideos = await getAllVideos()
      setVideos(storedVideos)

      const usage = await getStorageUsageFromDB()
      setStorageUsage(usage)
    } catch (err) {
      setError(err.message)
      // eslint-disable-next-line no-console
      console.error('Failed to load videos:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Delete a video
  const deleteVideo = useCallback(
    async videoId => {
      try {
        await deleteVideoFromDB(videoId)

        // Update local state
        setVideos(prevVideos => {
          const updatedVideos = prevVideos.filter(video => video.id !== videoId)

          // Clean up blob URLs to prevent memory leaks
          const deletedVideo = prevVideos.find(video => video.id === videoId)
          if (deletedVideo && deletedVideo.blobUrl) {
            URL.revokeObjectURL(deletedVideo.blobUrl)
          }

          return updatedVideos
        })

        // Force refresh of storage usage including browser totals
        await loadVideos()
      } catch (err) {
        setError(err.message)
        throw err
      }
    },
    [loadVideos]
  )

  // Clear all videos
  const clearAll = useCallback(async () => {
    try {
      await clearAllVideos()

      // Clean up blob URLs
      videos.forEach(video => {
        if (video.blobUrl) {
          URL.revokeObjectURL(video.blobUrl)
        }
      })

      // Force refresh of storage usage including browser totals
      await loadVideos()
    } catch (err) {
      setError(err.message)
      throw err
    }
  }, [videos, loadVideos])

  // Get storage usage
  const getStorageUsage = useCallback(async () => {
    try {
      const usage = await getStorageUsageFromDB()
      setStorageUsage(usage)
      return usage
    } catch (err) {
      setError(err.message)
      throw err
    }
  }, [])

  // Refresh videos list
  const refresh = useCallback(() => {
    loadVideos()
  }, [loadVideos])

  // Load videos on mount
  useEffect(() => {
    loadVideos()
  }, [loadVideos])

  // Cleanup blob URLs on unmount
  useEffect(() => {
    return () => {
      videos.forEach(video => {
        if (video.blobUrl) {
          URL.revokeObjectURL(video.blobUrl)
        }
      })
    }
  }, [videos])

  // Listen for storage events (when videos are added from other tabs/components)
  useEffect(() => {
    const handleStorageChange = async () => {
      // Add a small delay to ensure storage operations are complete
      await new Promise(resolve => setTimeout(resolve, 200))
      loadVideos()
    }

    // Listen for custom events when videos are added
    window.addEventListener('videoAdded', handleStorageChange)
    window.addEventListener('videoDeleted', handleStorageChange)

    return () => {
      window.removeEventListener('videoAdded', handleStorageChange)
      window.removeEventListener('videoDeleted', handleStorageChange)
    }
  }, [loadVideos])

  return {
    videos,
    storageUsage,
    loading,
    error,
    deleteVideo,
    clearAll,
    getStorageUsage,
    refresh,
  }
}

/**
 * Utility function to dispatch custom events for cross-component communication
 */
export function dispatchVideoEvent(eventType, data = null) {
  const event = new CustomEvent(eventType, { detail: data })
  window.dispatchEvent(event)
}
