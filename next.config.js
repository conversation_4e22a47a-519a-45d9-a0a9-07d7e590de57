// const { i18n } = require('./next-i18next.config')
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

const {
  PHASE_DEVELOPMENT_SERVER,
  PHASE_PRODUCTION_BUILD,
} = require('next/constants')

/** @type {(phase: string, defaultConfig: import("next").NextConfig) => Promise<import("next").NextConfig>} */
module.exports = async phase => {
  /** @type {import("next").NextConfig} */
  const nextConfig = {
    // swcMinify: true,
    reactStrictMode: true,
    // i18n,

    // https://nextjs.org/docs/messages/large-page-data
    experimental: {
      largePageDataBytes: 256 * 1024,
    },

    images: {
      deviceSizes: [384, 640, 768, 1024, 1280, 1920],
      imageSizes: [16, 32, 48, 64, 96, 128, 256],
      // domains: [process.env.NEXT_PUBLIC_IMAGES_CDN.replace('https://', '')], // Deprecated since Next.js v14 in favor of remotePatterns
      remotePatterns: [
        {
          protocol: 'https',
          hostname: process.env.NEXT_PUBLIC_IMAGES_CDN.replace('https://', ''),
          port: '',
        },
      ],
    },

    async rewrites() {
      return [
        // Sitemap redirects to use Next's API routes
        {
          source: '/sitemap.xml',
          destination: '/api/sitemap',
        },
        {
          source: '/sitemap-pages.xml',
          destination: '/api/sitemap/pages',
        },
        {
          source: '/sitemap-articles.xml',
          destination: '/api/sitemap/articles',
        },
        {
          source: '/sitemap-shows.xml',
          destination: '/api/sitemap/shows',
        },
        {
          source: '/sitemap-episodes.xml',
          destination: '/api/sitemap/episodes',
        },
        {
          source: '/sitemap-events.xml',
          destination: '/api/sitemap/events',
        },
        // Deep linking and manifest
        {
          source: '/.well-known/apple-app-site-association',
          destination: '/api/deeplinking/apple-app-site-association',
        },
        {
          source: '/.well-known/assetlinks.json',
          destination: '/api/deeplinking/assetlinks',
        },
        {
          source: '/site.webmanifest',
          destination: '/api/manifest',
        },
        {
          source: '/api/files/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL}/files/:path*`,
        },
      ]
    },
  }

  if (phase === PHASE_DEVELOPMENT_SERVER || phase === PHASE_PRODUCTION_BUILD) {
    const withSerwist = (await import('@serwist/next')).default({
      // Note: This is only an example. If you use Pages Router,
      // use something else that works, such as "service-worker/index.ts".
      swSrc: 'service-worker/index.js',
      swDest: 'public/sw.js',
      reloadOnOnline: true,
    })
    return withBundleAnalyzer(withSerwist(nextConfig))
  }

  return withBundleAnalyzer(nextConfig)
}
