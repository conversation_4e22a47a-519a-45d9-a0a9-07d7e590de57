/**
 * IndexedDB utilities for video storage
 */

const DB_NAME = 'VideoDownloadsDB'
const DB_VERSION = 1
const STORE_NAME = 'videos'

/**
 * Initialize IndexedDB
 */
export function initDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve(request.result)

    request.onupgradeneeded = event => {
      const db = event.target.result

      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' })
        store.createIndex('downloadedAt', 'downloadedAt', { unique: false })
        store.createIndex('originalUrl', 'originalUrl', { unique: false })
      }
    }
  })
}

/**
 * Store a video blob in IndexedDB
 */
export async function storeVideo(videoData) {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)

    const videoRecord = {
      id: generateVideoId(),
      title: videoData.title || extractTitleFromUrl(videoData.originalUrl),
      originalUrl: videoData.originalUrl,
      blob: videoData.blob,
      size: videoData.blob.size,
      type: videoData.blob.type,
      downloadedAt: new Date().toISOString(),
    }

    const request = store.add(videoRecord)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      // Dispatch event to notify other components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(
          new CustomEvent('videoAdded', { detail: videoRecord })
        )
      }
      resolve(videoRecord)
    }
  })
}

/**
 * Get all stored videos
 */
export async function getAllVideos() {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.getAll()

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      const videos = request.result.map(video => {
        // Create blob URL with Safari-compatible options
        const blobUrl = URL.createObjectURL(video.blob)

        return {
          ...video,
          blobUrl,
          // Keep reference to original blob for Safari compatibility
          blob: video.blob,
        }
      })
      resolve(videos)
    }
  })
}

/**
 * Delete a video by ID
 */
export async function deleteVideo(videoId) {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.delete(videoId)

    request.onerror = () => reject(request.error)
    request.onsuccess = () => {
      // Dispatch event to notify other components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(
          new CustomEvent('videoDeleted', { detail: { videoId } })
        )
      }
      resolve()
    }
  })
}

/**
 * Get storage usage statistics
 */
export async function getStorageUsage() {
  const videos = await getAllVideos()

  const totalSize = videos.reduce((sum, video) => sum + video.size, 0)

  // Get storage quota information
  let quota = null
  let usage = null
  let available = null

  try {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate()
      quota = estimate.quota || null
      usage = estimate.usage || null
      available = quota && usage ? quota - usage : null
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Could not get storage estimate:', error)
  }

  return {
    used: totalSize,
    count: videos.length,
    quota: quota,
    totalUsage: usage,
    available: available,
    videos: videos.map(video => ({
      id: video.id,
      title: video.title,
      size: video.size,
      downloadedAt: video.downloadedAt,
    })),
  }
}

/**
 * Clear all stored videos
 */
export async function clearAllVideos() {
  const db = await initDB()

  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite')
    const store = transaction.objectStore(STORE_NAME)
    const request = store.clear()

    request.onerror = () => reject(request.error)
    request.onsuccess = () => resolve()
  })
}

/**
 * Download video from URL and return blob
 */
export async function downloadVideoBlob(url, onProgress) {
  try {
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const contentLength = response.headers.get('content-length')
    const total = contentLength ? parseInt(contentLength, 10) : 0

    // Call progress immediately to show 0%
    if (onProgress) {
      onProgress(0)
    }

    if (!response.body) {
      throw new Error('ReadableStream not supported')
    }

    const reader = response.body.getReader()
    const chunks = []
    let received = 0

    // eslint-disable-next-line no-constant-condition
    while (true) {
      const { done, value } = await reader.read()

      if (done) break

      chunks.push(value)
      received += value.length

      // Always call progress callback, even without content-length
      if (onProgress) {
        if (total > 0) {
          onProgress((received / total) * 100)
        } else {
          // If no content-length, show indeterminate progress
          onProgress(Math.min(90, (received / 1024 / 1024) * 10)) // Rough estimate based on MB
        }
      }
    }

    // Final progress update
    if (onProgress) {
      onProgress(100)
    }

    const blob = new Blob(chunks, {
      type: response.headers.get('content-type') || 'video/mp4',
    })

    return blob
  } catch (error) {
    throw new Error(`Failed to download video: ${error.message}`)
  }
}

/**
 * Generate a unique video ID
 */
function generateVideoId() {
  return `video_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

/**
 * Extract title from URL
 */
function extractTitleFromUrl(url) {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const filename = pathname.split('/').pop()

    if (filename && filename.includes('.')) {
      return filename.split('.').slice(0, -1).join('.')
    }

    return filename || `Video from ${urlObj.hostname}`
  } catch {
    return 'Downloaded Video'
  }
}

/**
 * Check if URL is a valid video URL
 */
export function isValidVideoUrl(url) {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname.toLowerCase()

    // Check for common video file extensions
    const videoExtensions = [
      '.mp4',
      '.webm',
      '.ogg',
      '.avi',
      '.mov',
      '.wmv',
      '.flv',
      '.mkv',
      '.m4v',
      '.3gp',
    ]
    const hasVideoExtension = videoExtensions.some(ext =>
      pathname.endsWith(ext)
    )

    // Allow any HTTP/HTTPS URL for now - we'll validate the content type during download
    const isHttpUrl =
      urlObj.protocol === 'http:' || urlObj.protocol === 'https:'

    return (
      isHttpUrl &&
      (hasVideoExtension ||
        pathname.includes('video') ||
        pathname.includes('media'))
    )
  } catch {
    return false
  }
}
