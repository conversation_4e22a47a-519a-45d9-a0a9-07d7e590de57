import React, { useState } from 'react'
import dynamic from 'next/dynamic'

import { serverSideTranslations } from 'next-i18next/serverSideTranslations'

import useVideoDownload from '../hooks/useVideoDownload'
import useVideoStorage from '../hooks/useVideoStorage'
import { storeVideo, createTestVideoBlob } from '../utils/videoStorage'

const Button = dynamic(() => import('ui/buttons/Button'))
const Input = dynamic(() =>
  import('ui/data-entry/Input').then(mod => mod.Input)
)
const Alert = dynamic(() => import('ui/feedback/Alert'))

export default function Downloads() {
  const [videoUrl, setVideoUrl] = useState('')
  const [selectedVideo, setSelectedVideo] = useState(null)
  const [testProgress, setTestProgress] = useState(0)
  const [testDownloading, setTestDownloading] = useState(false)

  const { downloadVideo, isDownloading, downloadProgress, downloadError } =
    useVideoDownload()

  const {
    videos,
    deleteVideo,
    storageUsage,
    loading: storageLoading,
  } = useVideoStorage()

  const handleDownload = async () => {
    if (!videoUrl.trim()) return

    try {
      await downloadVideo(videoUrl)
      setVideoUrl('')
    } catch (error) {
      // Error is already handled by the hook
    }
  }

  const handleVideoSelect = video => {
    setSelectedVideo(video)
  }

  const handleDeleteVideo = async videoId => {
    await deleteVideo(videoId)
    if (selectedVideo && selectedVideo.id === videoId) {
      setSelectedVideo(null)
    }
  }

  const handleAddTestVideo = async () => {
    try {
      const testBlob = createTestVideoBlob()
      await storeVideo({
        originalUrl: 'test://sample-video.mp4',
        blob: testBlob,
        title: 'Test Video Sample',
      })
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to add test video:', error)
    }
  }

  const handleTestProgress = async () => {
    setTestDownloading(true)
    setTestProgress(5) // Start with 5% to make it immediately visible

    // Small delay to ensure state is updated
    await new Promise(resolve => setTimeout(resolve, 100))

    // Simulate download progress
    for (let i = 10; i <= 100; i += 10) {
      setTestProgress(i)
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    setTimeout(() => {
      setTestDownloading(false)
      setTestProgress(0)
    }, 1000)
  }

  const formatFileSize = bytes => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 md:text-4xl">
            Video Downloads
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Download and store videos locally for offline viewing
          </p>
        </div>

        {/* Download Form */}
        <div className="mb-8 rounded-lg bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Download Video
          </h2>

          {downloadError && (
            <div className="mb-4">
              <Alert variant="danger" title="Download Error">
                {downloadError}
              </Alert>
            </div>
          )}

          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Enter video URL (e.g., https://example.com/video.mp4)"
                value={videoUrl}
                onChange={e => setVideoUrl(e.target.value)}
                className="w-full"
                disabled={isDownloading || testDownloading}
              />
            </div>
            <Button
              onClick={handleDownload}
              disabled={!videoUrl.trim() || isDownloading || testDownloading}
              variant="primary"
              label={isDownloading ? 'Downloading...' : 'Download'}
            />
            <Button
              onClick={handleAddTestVideo}
              variant="secondary"
              label="Add Test Video"
              title="Add a test video for demonstration"
            />
            <Button
              onClick={handleTestProgress}
              variant="tertiary"
              label="Test Progress"
              title="Test the progress bar animation"
              disabled={isDownloading || testDownloading}
            />
          </div>

          {(isDownloading || testDownloading) && (
            <div className="mt-4">
              <div className="mb-2 flex justify-between text-sm text-gray-600">
                <span>
                  {testDownloading ? 'Testing Progress...' : 'Downloading...'}
                </span>
                <span>
                  {Math.round(
                    testDownloading ? testProgress : downloadProgress
                  )}
                  %
                </span>
              </div>
              <div
                className="relative h-2 w-full overflow-hidden rounded-full"
                style={{ backgroundColor: '#e5e7eb' }} // gray-200
              >
                <div
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    height: '100%',
                    width: `${Math.max(0, Math.min(100, testDownloading ? testProgress : downloadProgress))}%`,
                    backgroundColor: testDownloading ? '#059669' : '#2563eb', // green-600 : blue-600
                    borderRadius: '9999px',
                    transition: 'width 300ms ease-out',
                    minWidth:
                      (testDownloading ? testProgress : downloadProgress) > 0
                        ? '4px'
                        : '0px',
                  }}
                />
              </div>
              {/* Debug info - remove in production */}
              <div className="mt-1 text-xs text-gray-500">
                Progress:{' '}
                {(testDownloading ? testProgress : downloadProgress).toFixed(1)}
                % | Mode:{' '}
                {testDownloading
                  ? 'Test'
                  : isDownloading
                    ? 'Real Download'
                    : 'Idle'}{' '}
                | Width:{' '}
                {Math.max(
                  0,
                  Math.min(
                    100,
                    testDownloading ? testProgress : downloadProgress
                  )
                )}
                % | Color: {testDownloading ? 'Green' : 'Blue'}
              </div>
            </div>
          )}
        </div>

        {/* Storage Usage */}
        {!storageLoading && storageUsage && (
          <div className="mb-8 rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              Storage Usage
            </h2>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">
                {videos.length} video{videos.length !== 1 ? 's' : ''} stored
              </span>
              <span className="font-medium text-gray-900">
                {formatFileSize(storageUsage.used)} used
              </span>
            </div>
          </div>
        )}

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Video List */}
          <div className="lg:col-span-1">
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h2 className="mb-4 text-xl font-semibold text-gray-900">
                Downloaded Videos
              </h2>

              {storageLoading ? (
                <div className="text-center text-gray-500">Loading...</div>
              ) : videos.length === 0 ? (
                <div className="text-center text-gray-500">
                  No videos downloaded yet
                </div>
              ) : (
                <div className="space-y-3">
                  {videos.map(video => (
                    <button
                      key={video.id}
                      type="button"
                      className={`w-full cursor-pointer rounded-lg border p-3 text-left transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                        selectedVideo?.id === video.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200'
                      }`}
                      onClick={() => handleVideoSelect(video)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="truncate text-sm font-medium text-gray-900">
                            {video.title || 'Untitled Video'}
                          </h3>
                          <p className="mt-1 text-xs text-gray-500">
                            {formatFileSize(video.size)}
                          </p>
                          <p className="mt-1 text-xs text-gray-400">
                            {new Date(video.downloadedAt).toLocaleDateString()}
                          </p>
                        </div>
                        <Button
                          onClick={e => {
                            e.stopPropagation()
                            handleDeleteVideo(video.id)
                          }}
                          variant="danger"
                          size="xs"
                          label="Delete"
                        />
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Video Player */}
          <div className="lg:col-span-2">
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h2 className="mb-4 text-xl font-semibold text-gray-900">
                Video Player
              </h2>

              {selectedVideo ? (
                <div>
                  <div className="mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      {selectedVideo.title || 'Untitled Video'}
                    </h3>
                    <p className="text-sm text-gray-500">
                      Downloaded from: {selectedVideo.originalUrl}
                    </p>
                  </div>

                  <div className="aspect-video w-full overflow-hidden rounded-lg bg-black">
                    <video
                      controls
                      className="h-full w-full"
                      src={selectedVideo.blobUrl}
                      onError={() => {
                        // Video playback error - could show user-friendly error message
                      }}
                    >
                      <track kind="captions" />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                </div>
              ) : (
                <div className="flex h-64 items-center justify-center rounded-lg bg-gray-100">
                  <div className="text-center">
                    <div className="text-gray-400">
                      <svg
                        className="mx-auto h-12 w-12"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Select a video to play
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'en', ['common'])),
    },
  }
}
