import React from 'react'
import PropTypes from 'prop-types'

import dynamic from 'next/dynamic'

import { usePageContext } from 'components/PageProvider'
import { getDocumentUrl } from 'utils/documents'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Link = dynamic(() => import('ui/navigation/Link'))

export default function LinkList({
  children,
  className = '',
  id,
  color,
  items,
  title,
  variant = 'text',
}) {
  return (
    <div className={`space-y-4 ${className}`} id={id}>
      {title && <h3 className="text-xl">{title}</h3>}
      <div className="space-y-3">
        {Array.isArray(items)
          ? items.map((item, i) => (
              <LinkItem
                key={`list-item-${i}`}
                color={color}
                variant={variant}
                {...item}
              />
            ))
          : children}
      </div>
    </div>
  )
}
LinkList.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  color: PropTypes.oneOf([
    'default',
    'primary',
    'secondary',
    'tertiary',
    'success',
    'info',
    'warning',
    'danger',
  ]),
  id: PropTypes.string,
  items: PropTypes.arrayOf(PropTypes.shape(LinkItem.propTypes)),
  title: PropTypes.string,
  variant: PropTypes.oneOf(['simple', 'text']),
}

const colors = {
  primary: 'bg-color1-700 text-white dark:bg-color1-600 dark:text-color1-200',
  success: 'bg-success-200 text-gray-700',
  info: 'bg-info-200 text-gray-700',
  danger: 'bg-danger-200 text-gray-700',
  warning: 'bg-warning-200 text-gray-700',
}

export function LinkItem({
  active,
  color,
  extra,
  file,
  icon,
  label,
  type,
  variant,
  url,
}) {
  const colorClass = colors[color]
  const isDownload = type === 'download'
  const { site } = usePageContext()

  const variantMap = {
    simple: {
      backgroundColor: 'bg-gray-100',
      spacing: 'px-6 py-3',
    },
  }

  const { backgroundColor, spacing } = variantMap[variant] || {
    backgroundColor: 'bg-white',
    spacing: '',
  }

  return (
    <div
      className={`w-full rounded-lg ${backgroundColor} transition-colors duration-300 ease-in-out dark:bg-gray-700 ${
        active ? 'bg-color1-700' : ''
      }`}
    >
      <Link
        className={`flex flex-1 items-center space-x-4 rtl:space-x-reverse ${spacing}`}
        to={isDownload ? getDocumentUrl(file, site.entity) : url}
        disabled={active}
        download={isDownload}
      >
        {(icon || isDownload) && (
          <div
            className={`flex items-center justify-center rounded-md  leading-none text-3xl ${colorClass} ${
              colorClass ? 'h-6 w-6 p-1.5' : 'h-4 w-4'
            } ${active ? 'bg-transparent' : ''}`}
          >
            {typeof icon === 'string' || isDownload ? (
              <Icon
                name={icon || (isDownload ? '' : 'download')}
                className={`${active ? 'text-color1-700' : ''}`}
                size="22"
              />
            ) : (
              icon
            )}
          </div>
        )}
        <div
          className={`flex-1 text-base ${
            active ? 'font-bold text-white' : 'text-gray-800 dark:text-gray-100'
          }`}
        >
          {label}
        </div>
        {extra && <div className="text-gray-400">{extra}</div>}
      </Link>
    </div>
  )
}
LinkItem.propTypes = {
  active: PropTypes.bool,
  color: PropTypes.oneOf([
    'default',
    'primary',
    'success',
    'info',
    'warning',
    'danger',
  ]),
  extra: PropTypes.node,
  file: PropTypes.object,
  icon: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  label: PropTypes.string,
  type: PropTypes.oneOf(['link', 'download']),
  variant: PropTypes.oneOf(['simple', 'text']),
  url: PropTypes.string,
}
