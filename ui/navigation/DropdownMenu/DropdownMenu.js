import PropTypes from 'prop-types'
import Dropdown from 'ui/buttons/Dropdown'
import { getTextColor } from 'ui/helpers/getColor'
import { MenuItem } from '../Menu/components/MenuItem'
import { DropdownButton } from './DropdownButton'

export function DropdownMenu({
  activeColor,
  activeItemClassName,
  alignment,
  bgColor,
  border,
  borderRadius,
  className,
  direction,
  divider,
  icon,
  iconClassName,
  iconSize,
  itemClassName,
  itemColor,
  items,
  offset,
  openedIcon,
  openedIconClassName,
  panelClass,
}) {
  const colorClass = getTextColor(itemColor)

  return (
    <Dropdown
      alignment={alignment}
      bgColor={bgColor}
      border={border}
      borderRadius={borderRadius}
      button={({ open }) => (
        <DropdownButton
          className={iconClassName}
          icon={icon}
          iconSize={iconSize}
          open={open}
          openedIcon={openedIcon}
          openedClassName={openedIconClassName}
        />
      )}
      color="none"
      direction={direction}
      divider={divider}
      hasMaxHeight={false}
      hideArrow
      icon={icon}
      iconClass={colorClass}
      panelAs="nav"
      itemsAs="ul"
      itemsClass={className}
      offset={offset}
      openedIcon={openedIcon}
      panelClass={panelClass}
      size="sm"
      variant="flat"
    >
      {({ close }) =>
        items.map((item, key) => (
          <MenuItem
            key={key}
            {...item}
            activeColor={activeColor}
            activeClassName={activeItemClassName}
            className={itemClassName}
            onLinkClick={() => {
              close()
            }}
          />
        ))
      }
    </Dropdown>
  )
}

const menuItemShape = {
  label: PropTypes.string,
  url: PropTypes.string,
}

DropdownMenu.propTypes = {
  activeColor: PropTypes.string,
  activeItemClassName: PropTypes.string,
  alignment: Dropdown.propTypes.alignment,
  bgColor: PropTypes.string,
  border: PropTypes.object,
  borderRadius: PropTypes.object,
  button: PropTypes.oneOfType([PropTypes.node, PropTypes.func]),
  className: PropTypes.string,
  direction: PropTypes.string,
  divider: PropTypes.object,
  icon: PropTypes.string,
  iconClassName: PropTypes.string,
  iconSize: PropTypes.object,
  itemClassName: PropTypes.string,
  itemColor: PropTypes.string,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      ...menuItemShape,
      children: PropTypes.arrayOf(PropTypes.shape(menuItemShape)),
    })
  ),
  itemsClass: PropTypes.string,
  offset: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  openedIcon: PropTypes.string,
  openedIconClassName: PropTypes.string,
  panelClass: PropTypes.string,
}

export default DropdownMenu
