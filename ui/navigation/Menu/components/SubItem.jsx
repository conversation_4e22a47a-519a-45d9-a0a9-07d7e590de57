import clsx from 'clsx'
import PropTypes from 'prop-types'
import Icon from 'ui/icons/Icon'
import Link from 'ui/navigation/Link'

export function SubItem({
  active,
  className,
  icon = 'minus',
  iconClass,
  labelClass,
  label,
  marked,
  url,
  onClick,
}) {
  return url ? (
    <Link
      className={clsx(
        'flex rounded-md py-1 pl-2 transition-colors duration-300 ease-in-out',
        {
          'cursor-default': active,
          'hover:bg-gray-100': !active,
        }
      )}
      to={url}
      onClick={onClick}
    >
      <Icon
        className={clsx(
          'mr-2 mt-1 text-base',
          {
            'text-color1-700': marked,
            'text-gray-400': !marked,
          },
          iconClass
        )}
        name={icon}
      />
      <div
        className={clsx(
          {
            'font-semibold text-gray-900': active,
            'text-color1-700': !active,
          },
          labelClass
        )}
      >
        {label}
      </div>
    </Link>
  ) : (
    <div className="flex py-1 pl-2">
      <Icon
        className={clsx(
          'mr-2 mt-1 text-base',
          {
            'text-color1-700': marked,
            'text-gray-400': !marked,
          },
          iconClass
        )}
        name={icon}
      />
      <div className={className}>{label}</div>
    </div>
  )
}
SubItem.propTypes = {
  active: PropTypes.bool,
  className: PropTypes.string,
  icon: PropTypes.string,
  iconClass: PropTypes.string,
  label: PropTypes.string,
  labelClass: PropTypes.string,
  marked: PropTypes.bool,
  url: PropTypes.string,
  onClick: PropTypes.func,
}

export default SubItem
