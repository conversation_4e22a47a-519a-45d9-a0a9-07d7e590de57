import PropTypes from 'prop-types'
import { useCallback, useEffect, useRef, useState } from 'react'

import dynamic from 'next/dynamic'
import { useUserCookieConsent } from 'components/CookieConsentProvider'
import { useRouter } from 'next/router'

const Icon = dynamic(() => import('ui/icons/Icon'))
const Clickable = dynamic(() => import('ui/helpers/Clickable'))
const Link = dynamic(() => import('ui/navigation/Link'))
const SubItem = dynamic(() => import('./SubItem'))

export function MenuItem({
  active,
  activeClassName,
  children,
  className,
  label,
  onLinkClick,
  url,
  cookieOptions,
  site,
}) {
  const { cookieSettings } = site || {}
  const { asPath } = useRouter()

  const isActive =
    active ??
    ((asPath?.includes(url) && url !== '/') || (asPath === '/' && !url))

  const expandRef = useRef()
  const [isOpen, setIsOpen] = useState(active)

  const { setShowCookieConsent, disabled } = useUserCookieConsent()

  useEffect(() => {
    if (active && !isOpen && !expandRef.current) {
      setIsOpen(true)
      expandRef.current = true
    }
  }, [active, isOpen])

  const toggleSubItems = useCallback(() => {
    setIsOpen(!isOpen)
  }, [isOpen])

  if (cookieOptions === 'built-in' && cookieSettings?.disabled) return null

  const displayedClassName = isActive ? activeClassName ?? className : className

  return (
    <li className="flex flex-col">
      {!disabled && cookieOptions === 'built-in' ? (
        <Clickable
          className={displayedClassName}
          onClick={() => setShowCookieConsent(true)}
        >
          {label}
        </Clickable>
      ) : cookieOptions === 'one-trust' ? (
        <div id="ot-sdk-show-settings"></div>
      ) : cookieOptions === 'trust-arc' ? (
        <div id="teconsent"></div>
      ) : url ? (
        <Link className={displayedClassName} onClick={onLinkClick} to={url}>
          {label}
        </Link>
      ) : (
        <Clickable className={displayedClassName} onClick={toggleSubItems}>
          {label}
        </Clickable>
      )}

      {children?.length > 0 && (
        <button
          className="select-none rounded-full p-1 text-gray-600 transition-colors duration-300 ease-in-out hover:bg-white hover:text-color1-800"
          onClick={toggleSubItems}
        >
          <Icon name={isOpen ? 'chevron-up' : 'chevron-down'} />
        </button>
      )}

      {children?.length > 0 && isOpen && (
        <ul className="py-2 pl-2">
          {children.map((child, i) => (
            <SubItem key={i} {...child} onClick={onLinkClick} />
          ))}
        </ul>
      )}
    </li>
  )
}
MenuItem.propTypes = {
  active: PropTypes.bool,
  activeClassName: PropTypes.string,
  children: PropTypes.array,
  className: PropTypes.string,
  onLinkClick: PropTypes.func,
  url: PropTypes.string,
}
