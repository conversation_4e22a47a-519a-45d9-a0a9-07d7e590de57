import PropTypes from 'prop-types'

import { MenuItem } from './components/MenuItem'

export function Menu({
  activeItemClassName,
  className,
  itemClassName,
  items = [],
  site,
}) {
  return (
    <ul className={className ?? 'space-y-4'}>
      {items.map((item, key) => (
        <MenuItem
          key={`menu-item-${item.url}-${key}`}
          {...item}
          site={site}
          className={itemClassName}
          activeClassName={activeItemClassName}
        />
      ))}
    </ul>
  )
}
Menu.propTypes = {
  activeItemClassName: PropTypes.string,
  className: PropTypes.string,
  itemClassName: PropTypes.string,
  items: PropTypes.array,
}
