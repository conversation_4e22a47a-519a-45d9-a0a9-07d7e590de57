import PropTypes from 'prop-types'
import React, { Fragment } from 'react'

import { Dialog, Transition } from '@headlessui/react'
import { useTranslation } from 'next-i18next'

import Button from 'ui/buttons/Button'

import Image from '../Image'

export default function PictureDialog({
  file,
  onClose,
  open,
  pictureAlt,
  src,
}) {
  const { t } = useTranslation()

  return (
    <Transition
      show={open}
      enter="transition duration-100 ease-out"
      enterFrom="transform scale-95 opacity-0"
      enterTo="transform scale-100 opacity-100"
      leave="transition duration-75 ease-out"
      leaveFrom="transform scale-100 opacity-100"
      leaveTo="transform scale-95 opacity-0"
      as={Fragment}
    >
      <Dialog
        className="fixed inset-0 z-max flex h-full w-full items-center justify-center px-4 pb-4 pt-12 lg:px-8 lg:pb-8"
        onClose={onClose}
      >
        <div className="absolute inset-0 bg-black/80" />
        <Dialog.Panel
          as="figure"
          className="relative flex max-h-full max-w-full flex-col overflow-hidden"
        >
          <Image alt={pictureAlt} src={src} file={file} objectFit="contain" />
        </Dialog.Panel>
        <Button
          title={t('close')}
          size="lg"
          variant="flat"
          color="none"
          icon="times"
          onClick={onClose}
          className="absolute right-0 top-0 text-white"
        />
      </Dialog>
    </Transition>
  )
}

PictureDialog.propTypes = {
  file: PropTypes.object,
  onClose: PropTypes.func,
  open: PropTypes.bool,
  pictureAlt: PropTypes.string,
  src: PropTypes.string,
}
