import { useEffect, useState } from 'react'

import 'mapbox-gl/dist/mapbox-gl.css'
import dynamic from 'next/dynamic'
import { Marker } from 'react-map-gl/mapbox'

import useClickOutside from 'ui/helpers/useClickOutside'

const SvgWrap = dynamic(() => import('ui/icons/SvgWrap'))

/**
 * MapMarker component
 *
 * @param {Object} props Component props
 * @param {number[]} props.coordinates The coordinates for the marker
 * @param {boolean} props.current Indicates if this marker is the current one
 * @param {function} props.onClick Click event handler
 * @param {React.ReactNode} props.popover Popover content
 * @param {'church'|'point'} [props.type='point'] The type of the marker
 * @returns {React.ReactElement}  The map marker component
 */
export default function MapMarker({
  coordinates,
  current,
  onClick,
  popover,
  type = 'point',
}) {
  const { nodeRef, open, setOpen } = useClickOutside()
  const [wasOpenBefore, setWasOpenBefore] = useState(false)

  useEffect(() => {
    if (open && !wasOpenBefore) {
      setWasOpenBefore(true)
    }

    if (!open && wasOpenBefore) {
      setWasOpenBefore(false)
      onClick(null)
    }
  }, [open, onClick, wasOpenBefore, setWasOpenBefore])

  if (!Array.isArray(coordinates)) return null
  const [longitude, latitude] = coordinates

  return (
    <Marker longitude={longitude || 0} latitude={latitude || 0} anchor="bottom">
      <SvgWrap
        className={popover ? 'cursor-pointer' : undefined}
        style={undefined}
        width="36"
        height="36"
        viewBox="0 0 306 431"
        onClick={
          popover
            ? () => {
                onClick(coordinates)
                setOpen(true)
              }
            : undefined
        }
      >
        <g fill="none" fillRule="evenodd">
          <path
            className={type === 'point' ? 'fill-color1-900' : 'fill-color2-900'}
            d="M152.5,0 C236.17114,0 304.620813,67.8288677 304.620813,151.5 C304.620813,219.973246 212.478031,365.877654 179.061905,416.08347 C172.361452,426.150516 162.587519,430.12616 152.5,430.12616 C142.412481,430.12616 132.4531,426.289065 125.777579,416.08347 C83.4828109,351.422853 0.259447786,217 0.259447786,152 C0.259447786,68.3288604 68.8288604,0 152.5,0 Z M153,88 C117.653776,88 89,116.653776 89,152 C89,187.346224 117.653776,216 153,216 C188.346224,216 217,187.346224 217,152 C217,116.653776 188.346224,88 153,88 Z"
          />
          <circle
            cx="153"
            cy="152"
            r="64"
            fillOpacity=".9"
            className="fill-white"
          />
        </g>
      </SvgWrap>
      {current && open && popover && (
        <div className="absolute mt-3">
          <div className="relative -ml-[50%] mr-[50%] drop-shadow-lg">
            <span className="absolute left-1/2 -top-2 ml-4 block h-4 w-4 -translate-x-1/2 rotate-45 border-l border-t border-neutral-300 bg-white" />
            <div
              className="ml-4 flex flex-col items-center space-y-2 rounded-lg border border-neutral-300 bg-white px-3 py-2 text-center shadow-md"
              ref={nodeRef}
            >
              {popover}
            </div>
          </div>
        </div>
      )}
    </Marker>
  )
}
