import { useMemo } from 'react'
import { getResponsiveClasses } from './getResponsiveClasses'

/**
 * @typedef {'extralight'|'light'|'normal'|'semibold'|'bold'|'black'} FontWeight A font weight type

/**
 * Returns a responsive text weight class based on the provided weight and default weight.
 * 
 * @param {import('./getResponsiveClasses').ResponsiveValue} weight The font weight to apply (e.g., 'normal', 'bold', etc.) on different breakpoints (i.e. { xs: 'normal', sm: 'bold' }).
 * @param {FontWeight} [defaultWeight='normal'] The default font weight to fall back to if the provided weight is not valid.
 * @returns {string} The responsive font weight class.
 */
export default function useFontWeight(weight, defaultWeight = 'normal') {
  return useMemo(
    () => getResponsiveClasses('font', weight, fontWeights, defaultWeight),
    [weight, defaultWeight]
  )
}

const fontWeights = [
  'extralight', // font-extralight sm:font-extralight md:font-extralight lg:font-extralight xl:font-extralight 2xl:font-extralight
  'light', // font-light sm:font-light md:font-light lg:font-light xl:font-light 2xl:font-light
  'normal', // font-normal sm:font-normal md:font-normal lg:font-normal xl:font-normal 2xl:font-normal
  'semibold', // font-semibold sm:font-semibold md:font-semibold lg:font-semibold xl:font-semibold 2xl:font-semibold
  'bold', // font-bold sm:font-bold md:font-bold lg:font-bold xl:font-bold 2xl:font-bold
  'black', // font-black sm:font-black md:font-black lg:font-black xl:font-black 2xl:font-black
]
