import clsx from 'clsx'

import { useGridItemClasses } from './useGridItem'
import { useFlexClasses } from './useFlexClasses'

export function useContentClasses(props = {}, parent, extraClassName = '') {
  const gridItemClasses = useGridItemClasses(props, parent)
  const flexClasses = useFlexClasses(props, parent)
  return clsx(props.className, props?.hideInPrint && 'print:hidden', gridItemClasses, flexClasses, extraClassName)
}
