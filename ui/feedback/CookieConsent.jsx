import React, { Fragment, useEffect, useState } from 'react'
import PropTypes from 'prop-types'

import Head from 'next/head'
import Script from 'next/script'
import dynamic from 'next/dynamic'

import useCode from 'utils/useCode'
import { useUserCookieConsent } from 'components/CookieConsentProvider'
import { getDefaultValues } from 'utils/objects'

const Button = dynamic(() => import('ui/buttons/Button'))
const ButtonToggle = dynamic(() => import('ui/buttons/ButtonToggle'))
const Accordion = dynamic(() => import('ui/data-display/Accordion'))
const AccordionItem = dynamic(() =>
  import('ui/data-display/Accordion').then(m => m.AccordionItem)
)

const defaultCookieText = {
  popupTitle: 'We use cookies',
  popupDescription:
    'We use cookies and other technologies to improve your browsing experience on our website, to show you personalized content and to analyze our website traffic to understand where our visitors are coming from.',
  necessary: 'Strictly necessary cookies',
  necessaryHelp:
    'These cookies are essential to provide you with services available through our website and to enable you to use certain features of our website. Without these cookies, we cannot provide you certain services on our website.',
  functional: 'Functionality cookies',
  functionalHelp:
    'These cookies are used to provide you with a more personalized experience on our website and to remember choices you make when you use our website. For example, we may use functionality cookies to remember your language preferences or remember your login details.',
  tracking: 'Tracking and performance cookies',
  trackingHelp:
    'These cookies are used to collect information to analyze the traffic to our website and how visitors are using our website. For example, these cookies may track things such as how long you spend on the website or the pages you visit which helps us to understand how we can improve our website site for you. The information collected through these tracking and performance cookies do not identify any individual visitor.',
  targeting: 'Targeting and advertising cookies',
  targetingHelp:
    "These cookies are used to show advertising that is likely to be of interest to you based on your browsing habits. These cookies, as served by our content and/or advertising providers, may combine information they collected from our website with other information they have independently collected relating to your web browser's activities across their network of websites. If you choose to remove or disable these targeting or advertising cookies, you will still see adverts but they may not be relevant to you.",
  allowAll: 'Allow everything',
  allowSelected: 'Allow selected',
}

export default function CookieConsent({
  scripts = [],
  analytics = {
    enabled: false,
    domain: '',
  },
  settings = {},
}) {
  const { showCookieConsent, userConsent, updateUserConsent, disabled } =
    useUserCookieConsent()
  const { functional, tracking, targeting } = userConsent || {}

  // Sets temporary/ephimeral state for the toggle buttons
  // (so scripts are no added to the page until the user hits one of the "accept" buttons)
  const [functionalTemp, setFunctionalTemp] = useState()
  const [trackingTemp, setTrackingTemp] = useState()
  const [targetingTemp, setTargetingTemp] = useState()

  // Keeps temporary states updated with store values
  useEffect(() => {
    if (disabled) return
    setFunctionalTemp(functional)
  }, [functional, disabled])

  useEffect(() => {
    if (disabled) return
    setTrackingTemp(tracking)
  }, [tracking, disabled])

  useEffect(() => {
    if (disabled) return
    setTargetingTemp(targeting)
  }, [targeting, disabled])

  // If cookie consent is disabled, render all scripts and analytics anyways.
  // (website ownwers are responsible for showing the cookie consent banner in this case).
  if (disabled) {
    return (
      <>
        {scripts.map(script => (
          <HtmlScript script={script} key={script.id} />
        ))}
        {analytics.enabled && <AnalyticsScript domain={analytics.domain} />}
      </>
    )
  }

  const hasFunctionalCookies = scripts.some(
    script => script.cookieType === 'functional'
  )
  const hasTrackingCookies = scripts.some(
    script => script.cookieType === 'tracking'
  )
  const hasTargetingCookies = scripts.some(
    script => script.cookieType === 'targeting'
  )

  const {
    popupTitle,
    popupDescription,
    necessary,
    necessaryHelp,
    functional: functionalText,
    functionalHelp,
    tracking: trackingText,
    trackingHelp,
    targeting: targetingText,
    targetingHelp,
    allowAll,
    allowSelected,
  } = getDefaultValues(settings.cookieText, defaultCookieText)

  return (
    <div
      className={`transition-colors duration-300 ease-in-out ${
        !showCookieConsent && (!userConsent || userConsent?.necessary)
          ? 'hidden'
          : 'fixed inset-0 z-max flex items-center justify-center bg-black bg-opacity-25 p-4 sm:p-6 md:p-8'
      }`}
    >
      <div className="flex max-h-screen max-w-2xl flex-col space-y-8 overflow-hidden overflow-y-auto rounded-lg bg-white p-8 shadow-2xl">
        <div className="space-y-4">
          <h2 className="text-2xl font-bold">{popupTitle}</h2>
          <p>{popupDescription}</p>

          <Accordion>
            <AccordionItem
              title={necessary}
              description={necessaryHelp}
              extra={<ButtonToggle active disabled />}
            />
            {hasFunctionalCookies && (
              <AccordionItem
                title={functionalText}
                description={functionalHelp}
                extra={
                  <ButtonToggle
                    active={functionalTemp}
                    onChange={() => setFunctionalTemp(!functionalTemp)}
                  />
                }
              />
            )}
            {hasTrackingCookies && (
              <AccordionItem
                title={trackingText}
                description={trackingHelp}
                extra={
                  <ButtonToggle
                    active={trackingTemp}
                    onChange={() => setTrackingTemp(!trackingTemp)}
                  />
                }
              />
            )}
            {hasTargetingCookies && (
              <AccordionItem
                title={targetingText}
                description={targetingHelp}
                extra={
                  <ButtonToggle
                    active={targetingTemp}
                    onChange={() => setTargetingTemp(!targetingTemp)}
                  />
                }
              />
            )}
          </Accordion>
        </div>
        <div className="flex flex-col items-stretch justify-between space-y-4 tracking-wide md:flex-row md:space-x-4 md:space-y-0 rtl:md:space-x-reverse">
          <Button
            label={allowAll}
            variant="primary"
            onClick={() => {
              updateUserConsent({
                necessary: true,
                functional: true,
                tracking: true,
                targeting: true,
              })
            }}
          />
          <Button
            label={allowSelected}
            variant="primary"
            onClick={() => {
              updateUserConsent({
                necessary: true,
                functional: functionalTemp,
                tracking: trackingTemp,
                targeting: targetingTemp,
              })
            }}
          />
        </div>
      </div>

      {scripts
        .filter(({ cookieType }) =>
          cookieType === 'none' ? true : userConsent?.[cookieType] ?? false
        )
        .map(script => (
          <HtmlScript script={script} key={script.id} />
        ))}

      {analytics.enabled && <AnalyticsScript domain={analytics.domain} />}
    </div>
  )
}
CookieConsent.propTypes = {
  analytics: PropTypes.shape({
    enabled: PropTypes.bool,
    domain: PropTypes.string,
  }),
  scripts: PropTypes.array,
  settings: PropTypes.shape({
    disabled: PropTypes.bool,
    cookieText: PropTypes.object,
  }),
}

function HtmlScript({ script }) {
  const renderedHtml = useCode(
    script.code,
    script.id,
    script.head ? 'beforeInteractive' : 'afterInteractive'
  )

  const codeNodes = Array.isArray(renderedHtml)
    ? renderedHtml
    : [{ ...renderedHtml, key: `script-node-${script.id}` }]

  return script.head ? (
    <>
      {/* Add all nodes that aren't `script` tags to the head directly */}
      <Head>{codeNodes.filter(node => node.type?.name !== 'Script')}</Head>

      {/* And add all `script` tags to the body, but as useCode converts them to Next's Script components and when the head flag is set, the Script's 'beforeInteractive' strategy will set them to the head anyways (Next's Scripts cannot be added into <Head>) */}
      <Fragment>
        {codeNodes.filter(node => node.type?.name === 'Script')}
      </Fragment>
    </>
  ) : (
    <Fragment>{codeNodes}</Fragment>
  )
}
HtmlScript.propTypes = {
  script: PropTypes.shape({
    code: PropTypes.string,
    id: PropTypes.string,
    head: PropTypes.bool,
  }),
}

function AnalyticsScript({ domain }) {
  if (!domain) return null

  return (
    <Script
      defer
      data-domain={domain}
      src="https://analytics.hopeplatform.org/js/plausible.js"
      strategy="afterInteractive"
    />
  )
}
AnalyticsScript.propTypes = {
  domain: PropTypes.string,
}
